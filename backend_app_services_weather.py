from __future__ import annotations

"""Open‑Meteo weather proxy / cache service.

This module wraps all interaction with the Open‑Meteo API and stores a small
cache in the ``weather_cache`` collection to stay well under the free‑tier
limits (≤ 12 calls / city / day).  The cache TTL is configurable via
``app.config['WEATHER_CACHE_TTL']`` and defaults to 7 200 s (2 h).

Usage
-----
>>> from app.services.weather import get_weather
>>> payload = get_weather("stl")  # St Louis, MO

The returned *payload* is the raw JSON dict from Open‑Meteo.  The front‑end
is responsible for rendering current conditions and a 3‑day outlook.
"""

from datetime import datetime, timezone
import json
from typing import Any, Dict

import requests
from flask import current_app

from app.extensions import db

# Define a simple SQLAlchemy model for caching weather data
class WeatherCache(db.Model):
    __tablename__ = 'weather_cache'
    city_code = db.Column(db.String(10), primary_key=True)
    fetched_at = db.Column(db.DateTime, nullable=False)
    # Store payload as TEXT and serialize/deserialize JSON
    payload_json = db.Column(db.Text, nullable=False)

    def get_payload(self):
        return json.loads(self.payload_json)

    def set_payload(self, payload: Dict[str, Any]):
        self.payload_json = json.dumps(payload)

# ---------------------------------------------------------------------------
# City → coordinates mapping (extend as needed)
# ---------------------------------------------------------------------------

_CITIES: Dict[str, Dict[str, float]] = {
    "stl": {"lat": 38.6270, "lon": -90.1994},  # St Louis, Missouri, USA
    "acc": {"lat": 5.6037, "lon": -0.1870},   # Accra, Ghana
}


# ---------------------------------------------------------------------------
# Public helper
# ---------------------------------------------------------------------------


def get_weather(city_code: str) -> Dict[str, Any]:
    """Return current + 3‑day weather payload for *city_code*.

    Results are cached in MongoDB for ``WEATHER_CACHE_TTL`` seconds to minimise
    external API calls.  If an error occurs during fetch, the stale cache (if
    any) is returned to keep the dashboard functional.
    """

    city_code = city_code.lower()
    if city_code not in _CITIES:
        raise ValueError(f"Unsupported city code: {city_code}")

    cfg_ttl: int = int(current_app.config.get("WEATHER_CACHE_TTL", 7200))
    now = datetime.utcnow().replace(tzinfo=timezone.utc)

    # Check cached doc
    cached_entry = WeatherCache.query.get(city_code)
    if cached_entry:
        age = (now - cached_entry.fetched_at).total_seconds()
        if age < cfg_ttl: # fresh enough
            return cached_entry.get_payload()

    # Build request
    coords = _CITIES[city_code]
    params = {
        "latitude": coords["lat"],
        "longitude": coords["lon"],
        "temperature_unit": "fahrenheit",
        "forecast_days": 3,
        "timezone": "auto",
        "current": "temperature_2m",
        "daily": "temperature_2m_max,temperature_2m_min,weather_code",
    }

    try:
        resp = requests.get("https://api.open-meteo.com/v1/forecast", params=params, timeout=5)
        resp.raise_for_status()
        payload: Dict[str, Any] = resp.json()
    except Exception as exc:
        current_app.logger.warning("Weather fetch failed for %s: %s", city_code, exc)
        if cached_entry: # return stale cache
            return cached_entry.get_payload()
        raise

    # Upsert cache
    if not cached_entry:
        cached_entry = WeatherCache(city_code=city_code)
        db.session.add(cached_entry)
    cached_entry.fetched_at = now
    cached_entry.set_payload(payload)
    db.session.commit()
    return payload
