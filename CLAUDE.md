# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (Flask)
```bash
# Setup
python3 -m venv venv && source venv/bin/activate
pip install -r backend_requirements.txt

# Development server  
flask --app backend_wsgi run -p 8000

# Production deployment
gunicorn -c backend_gunicorn_config.py backend_wsgi:application

# Testing
pytest

# Code formatting
black .
flake8 .
```

### Frontend (React + Vite)
```bash
# Setup
npm install

# Development server (runs on localhost:5173)
npm run dev

# Production build
npm run build

# Preview production build
npm run preview --port 4173
```

### Environment Setup
- Backend: Copy `backend_env.txt` to `backend/.env` and configure
- Frontend: Copy `frontend_env.txt` to `frontend/.env` and configure
- MongoDB: Default connection assumes local MongoDB on standard port

## Architecture Overview

### Backend Structure
- **Flask application factory pattern** in `backend_app_init.py`
- **Configuration classes** in `backend_app_config.py` (Development/Production)
- **Extensions centralized** in `backend_app_extensions.py` (SQLAlchemy, Flask-Login, CORS, etc.)
- **Blueprint-based routing** with modular route files (`backend_app_routes_*.py`)
- **Models** use SQLAlchemy with Flask-Login integration (`backend_app_models_*.py`)
- **API prefix**: All routes under `/api/`

### Frontend Structure  
- **Vite-powered React 18** with development proxy to backend port 8000
- **React Router** with lazy loading and protected routes
- **React Query** for server state management (5-minute stale time)
- **Authentication Context** provides global auth state
- **Dashboard with draggable widgets** using React Grid Layout
- **Widget-based architecture**: Weather, Bills, Events, Todos, Contacts

### Data Models
The application manages these core entities:
- **Users**: Authentication with dashboard layout preferences
- **Bills**: Financial tracking with RRULE recurring logic  
- **Events**: Calendar appointments with RFC 5545 recurrence
- **Todos**: Task management with TTL cleanup
- **Contacts**: Relationship management with color-coded thresholds
- **Contact Types**: Configurable contact categories
- **Weather Cache**: Open-Meteo API proxy with 2-hour TTL

### File Naming Convention
All files in this repository use a flattened naming scheme: `{directory}_{subdirectory}_{filename}.{ext}` (e.g., `backend_app_routes_auth.py` represents `backend/app/routes/auth.py`)

### Authentication & Security
- **Flask-Login** session-based authentication
- **Flask-Limiter** rate limiting (5 requests/min default)
- **CORS** configured for development and production origins
- **Password hashing** with Werkzeug utilities

### Production Deployment
- **Gunicorn** WSGI server with configuration in `backend_gunicorn_config.py`
- **systemd service** defined in `backend_systemd_personal_organizer.txt`
- **MongoDB** with TTL indexes for automatic cleanup
- **Cloudflare Tunnel** support for HTTPS

## Development Notes

### MongoDB to SQLAlchemy Migration
The codebase shows evidence of migration from MongoDB to SQLAlchemy - some models retain JSON fields for flexible data storage while using relational patterns.

### API Design Patterns
- Consistent JSON request/response format
- Blueprint organization by resource type
- Authentication required for all API endpoints except `/auth/login`
- Healthcheck endpoint at `/healthz`

### Frontend State Management
- **Server state**: React Query with automatic caching and refetching
- **Authentication state**: React Context provider pattern
- **Local state**: Component-level useState for UI interactions
- **No global state library**: Keeps architecture focused and simple

### Widget System
Dashboard widgets are self-contained React components that:
- Fetch their own data via React Query
- Handle loading and error states independently  
- Support drag-and-drop repositioning
- Adapt to responsive grid layouts