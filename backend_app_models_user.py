from __future__ import annotations

"""User model helpers wrapping MongoDB documents.

This app is strictly *single‑tenant / single‑user*, but we keep a thin wrapper
around the ``users`` collection so future multi‑user expansion remains easy.
Everything is static‑method driven to avoid introducing a heavyweight ODM.
"""

from datetime import datetime
from typing import Any, Dict, Optional

from flask_login import UserMixin
from werkzeug.security import check_password_hash, generate_password_hash

from app.extensions import db
from sqlalchemy.types import JSON # For dashboard_layout, categories


class User(UserMixin):
    """Lightweight adapter that plugs MongoDB docs into Flask‑Login."""

    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    pw_hash = db.Column(db.String(128), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    # Use JSON type for structured data, or db.Text if SQLite version is old
    dashboard_layout = db.Column(JSON, default={})
    categories = db.Column(JSON, default=[])
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define relationships if needed (e.g., user has many bills, events, etc.)
    # bills = db.relationship('Bill', backref='user', lazy=True)
    # events = db.relationship('Event', backref='user', lazy=True)
    # todos = db.relationship('Todo', backref='user', lazy=True)
    # contacts = db.relationship('Contact', backref='user', lazy=True)

    # ---------------------------------------------------------------------
    # Core lookup helpers (class/static methods)
    # ---------------------------------------------------------------------

    @staticmethod
    def _by_query(query: Dict[str, Any]) -> "User | None":
        # This method will be replaced by direct SQLAlchemy queries
        raise NotImplementedError("Use SQLAlchemy query methods directly")

    @classmethod
    def get(cls, user_id: int) -> "User | None": # ID is now int
        """Find a user by primary key."""
        return cls.query.get(user_id)

    @classmethod
    def by_username(cls, username: str) -> "User | None":
        return cls.query.filter_by(username=username).first()

    # ---------------------------------------------------------------------
    # Creation & authentication
    # ---------------------------------------------------------------------

    @classmethod
    def create(cls, *, username: str, password: str, email: str | None = None, phone: str | None = None) -> "User":
        """Insert a new user and return its adapter instance."""

        user = cls(
            username=username,
            pw_hash=generate_password_hash(password),
            email=email,
            phone=phone,
            dashboard_layout={},
            categories=[],
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        db.session.add(user)
        db.session.commit()
        return user

    # ---------------------------------------------------------------------
    # Instance helpers
    # ---------------------------------------------------------------------

    def verify_password(self, password: str) -> bool:
        return check_password_hash(self.pw_hash, password)

    def update_profile(self, *, username: Optional[str] = None, email: Optional[str] = None, phone: Optional[str] = None) -> None:
        """Partial update of profile fields (no password)."""
        # SQLAlchemy tracks changes automatically, just assign new values
        if username:
            update["username"] = username
        if email is not None:  # allow clearing by passing ""
            update["email"] = email or None
        if phone is not None:
            update["phone"] = phone or None
        
        if username:
            self.username = username
        if email is not None:
            self.email = email or None
        if phone is not None:
            self.phone = phone or None
        self.updated_at = datetime.utcnow() # onupdate handles this, but explicit is fine
        db.session.commit()

    def set_password(self, new_password: str) -> None:
        new_hash = generate_password_hash(new_password)
        self.pw_hash = new_hash
        self.updated_at = datetime.utcnow()
        db.session.commit()

    # ---------------------------------------------------------------------
    # Flask‑Login required properties
    # ---------------------------------------------------------------------

    @property
    def is_authenticated(self) -> bool:  # pragma: no cover – inherited OK
        return True

    @property
    def is_active(self) -> bool:  # pragma: no cover
        return True

    @property
    def is_anonymous(self) -> bool:  # pragma: no cover
        return False

    def get_id(self) -> str:  # pragma: no cover
        return self.id

    # ---------------------------------------------------------------------
    # Serialisation helper (for API responses)
    # ---------------------------------------------------------------------

    def to_dict(self) -> Dict[str, Any]:
        """Return a safe serialisable representation (no password hash)."""
        return {
            "id": self.id,
            "username": self.username,
            "email": self.email,
            "phone": self.phone,
            "dashboard_layout": self.dashboard_layout,
            "categories": self.categories,
        }
