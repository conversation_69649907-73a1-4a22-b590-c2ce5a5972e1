"""Centralised extension instances and one-call initialisation.

Import the instances elsewhere instead of creating new ones to avoid circular
imports (e.g. `from app.extensions import mongo`).
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, AnonymousUserMixin
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

# ---------------------------------------------------------------------------
# Singleton extension instances
# ---------------------------------------------------------------------------

db = SQLAlchemy()
login_manager: LoginManager = LoginManager()
limiter: Limiter = Limiter(key_func=get_remote_address)

# CORS does not need to be stored globally (no additional state), but we keep a
# reference for type‑checking/completeness.
_cors: CORS = CORS()


# ---------------------------------------------------------------------------
# Anonymous user class to satisfy Flask‑Login type hints in a single‑user app.
# ---------------------------------------------------------------------------


class _Anonymous(AnonymousUserMixin):
    """A no‑op anonymous user (all permissions = False)."""

    def __init__(self):
        self.username = "Anonymous"

    def __repr__(self) -> str:  # pragma: no cover
        return "<AnonymousUser>"


# ---------------------------------------------------------------------------
# Public helper
# ---------------------------------------------------------------------------


def init_extensions(app: Flask) -> None:
    """Initialise all extensions with the given *app* instance."""

    # MongoDB
    db.init_app(app)

    # Login manager
    login_manager.init_app(app)
    login_manager.login_view = "auth.login"  # blueprint endpoint string
    login_manager.anonymous_user = _Anonymous

    # Rate limiter (reads config.RATELIMIT_DEFAULT on `init_app`)
    limiter.init_app(app)

    # CORS – allow the Vite dev server and the production domain (configurable)
    allowed_origins = app.config.get("CORS_ALLOWED_ORIGINS", [
        "http://localhost:5173",
        "http://127.0.0.1:5173",
    ])
    _cors.init_app(
        app,
        resources={r"/api/*": {"origins": allowed_origins}},
        supports_credentials=True,
    )

    # Log confirmation for debugging
    app.logger.debug("Extensions initialised: db=%s, limiter=%s", db, limiter)
